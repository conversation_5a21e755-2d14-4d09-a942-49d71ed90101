import { useMemo } from 'react';
import useCurrentUser from '../../../../data/hooks/useCurrentUser';
import { IFroalaConfig } from './TextEditor';

const useEvents = ({
  fileCategory,
  events = {},
  setLoading, // eslint-disable-line @typescript-eslint/no-unused-vars
  onFileUpload, // eslint-disable-line @typescript-eslint/no-unused-vars
  setIsFocus,
  setCounter,
}: {
  fileCategory?: string;
  events?: IFroalaConfig['events'];
  setLoading?: (isLoading: boolean) => void;
  onFileUpload?: (fileId: string) => void;
  setIsFocus?: React.Dispatch<React.SetStateAction<boolean>>;
  setCounter: React.Dispatch<React.SetStateAction<number>>;
}) => {
  const { me } = useCurrentUser();

  return useMemo(() => {
    const activeUploads = {};
    const basicEvents = {
      focus() {
        setIsFocus && setIsFocus(true);
      },
      blur() {
        setIsFocus && setIsFocus(false);
      },
      'charCounter.update'() {
        const count = (this as any).charCounter;
        setCounter(count?.count());
      },
      ...events,
    };

    if (!fileCategory || !me) {
      return basicEvents;
    }

    const allEvents = {
      'edit.on'() {
        if (Object.keys(activeUploads).length > 0) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (this as any).edit.off();
        }
      },
      ...basicEvents,
    };
    return allEvents;
  }, [events, fileCategory, me, setIsFocus, setCounter]);
};

export default useEvents;
